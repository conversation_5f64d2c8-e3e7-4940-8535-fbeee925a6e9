import { useMemo } from 'react';
import { useReadContracts } from 'wagmi';
import { Address } from 'viem';
import Token from '@/types/token.ts';
import { EvmNetwork, TokenBalance } from '@/lib/EvmNetwork.ts';

/**
 * 批量查询代币余额的自定义 Hook
 * 使用 Wagmi 的 useReadContracts 实现 multiCall 功能
 */
export function useTokenBalances(
  tokens: Token[],
  userAddress?: Address,
  network?: EvmNetwork,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
    staleTime?: number;
  }
) {
  const {
    enabled = true,
    refetchInterval = 30000, // 30秒刷新一次
    staleTime = 10000, // 10秒内认为数据新鲜
  } = options || {};

  // 创建合约调用配置
  const contracts = useMemo(() => {
    if (!userAddress || !network || !tokens.length) {
      return [];
    }

    return network.createTokenBalanceContracts(tokens, userAddress);
  }, [tokens, userAddress, network]);

  // 执行批量合约调用
  const {
    data: results,
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useReadContracts({
    contracts,
    query: {
      enabled: enabled && !!userAddress && contracts.length > 0,
      refetchInterval,
      staleTime,
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  });

  // 处理结果并格式化
  const tokenBalances = useMemo(() => {
    if (!results || !network) {
      return [];
    }

    return network.processTokenBalanceResults(tokens, results);
  }, [results, tokens, network]);

  // 计算总价值（如果需要的话，这里可以扩展）
  const totalBalance = useMemo(() => {
    return tokenBalances.reduce((total, tokenBalance) => {
      // 这里可以添加价格计算逻辑
      // 目前只是简单地计算代币数量
      return total + Number(tokenBalance.formattedBalance);
    }, 0);
  }, [tokenBalances]);

  // 过滤出有余额的代币
  const tokensWithBalance = useMemo(() => {
    return tokenBalances.filter(
      (tokenBalance) => tokenBalance.balance > 0n && !tokenBalance.error
    );
  }, [tokenBalances]);

  // 过滤出有错误的代币
  const tokensWithError = useMemo(() => {
    return tokenBalances.filter((tokenBalance) => !!tokenBalance.error);
  }, [tokenBalances]);

  return {
    // 原始数据
    tokenBalances,
    
    // 过滤后的数据
    tokensWithBalance,
    tokensWithError,
    
    // 统计信息
    totalBalance,
    totalTokens: tokens.length,
    tokensWithBalanceCount: tokensWithBalance.length,
    tokensWithErrorCount: tokensWithError.length,
    
    // 状态
    isLoading,
    isRefetching,
    error,
    
    // 操作
    refetch,
  };
}

/**
 * 查询单个代币余额的简化 Hook
 */
export function useTokenBalance(
  token: Token,
  userAddress?: Address,
  network?: EvmNetwork,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
    staleTime?: number;
  }
) {
  const result = useTokenBalances([token], userAddress, network, options);
  
  return {
    tokenBalance: result.tokenBalances[0] || null,
    isLoading: result.isLoading,
    isRefetching: result.isRefetching,
    error: result.error,
    refetch: result.refetch,
  };
}

/**
 * 获取用户所有代币余额的 Hook
 * 结合 tokenStore 使用
 */
export function useUserTokenBalances(
  userAddress?: Address,
  network?: EvmNetwork,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
    staleTime?: number;
    onlyWithBalance?: boolean; // 是否只返回有余额的代币
  }
) {
  // 这里可以从 tokenStore 获取代币列表
  // 为了示例，我们先使用空数组
  const tokens: Token[] = []; // 实际使用时应该从 store 获取
  
  const result = useTokenBalances(tokens, userAddress, network, options);
  
  const filteredBalances = useMemo(() => {
    if (options?.onlyWithBalance) {
      return result.tokensWithBalance;
    }
    return result.tokenBalances;
  }, [result.tokenBalances, result.tokensWithBalance, options?.onlyWithBalance]);
  
  return {
    ...result,
    tokenBalances: filteredBalances,
  };
}
