import React from 'react';
import { observer } from 'mobx-react-lite';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Spinner } from '@heroui/react';
import { Address } from 'viem';
import { useStore } from '@/store';
import { useTokenBalances, useTokenBalance } from '@/hooks/useTokenBalances';
import { EvmNetwork } from '@/lib/EvmNetwork';
import { networks } from '@/config/network';
import { formatBalance } from '@/lib/utils/util';

/**
 * 批量代币余额查询示例组件
 */
const TokenBalancesExample = observer(() => {
  const { address } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const { tokenStore } = useStore();

  // 获取当前网络
  const currentNetwork = networks.find((net) => net.chainId === chainId);
  const evmNetwork = currentNetwork instanceof EvmNetwork ? currentNetwork : undefined;

  // 使用批量查询 Hook
  const {
    tokenBalances,
    tokensWithBalance,
    tokensWithError,
    totalTokens,
    tokensWithBalanceCount,
    tokensWithErrorCount,
    isLoading,
    isRefetching,
    error,
    refetch,
  } = useTokenBalances(
    tokenStore.tokenList, // 从 store 获取代币列表
    address as Address,
    evmNetwork,
    {
      enabled: !!address && !!evmNetwork,
      refetchInterval: 30000, // 30秒自动刷新
      staleTime: 10000, // 10秒内认为数据新鲜
    }
  );

  if (!address) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardBody className="text-center">
          <p className="text-gray-500">请先连接钱包</p>
        </CardBody>
      </Card>
    );
  }

  if (!evmNetwork) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardBody className="text-center">
          <p className="text-gray-500">当前网络不支持</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-4">
      {/* 统计信息 */}
      <Card>
        <CardHeader className="flex justify-between items-center">
          <h2 className="text-xl font-bold">代币余额总览</h2>
          <Button
            size="sm"
            variant="flat"
            onPress={() => refetch()}
            isLoading={isRefetching}
          >
            {isRefetching ? '刷新中...' : '刷新'}
          </Button>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{totalTokens}</p>
              <p className="text-sm text-gray-500">总代币数</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{tokensWithBalanceCount}</p>
              <p className="text-sm text-gray-500">有余额</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{tokensWithErrorCount}</p>
              <p className="text-sm text-gray-500">查询失败</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-600">
                {totalTokens - tokensWithBalanceCount - tokensWithErrorCount}
              </p>
              <p className="text-sm text-gray-500">零余额</p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* 加载状态 */}
      {isLoading && (
        <Card>
          <CardBody className="text-center">
            <Spinner size="lg" />
            <p className="mt-2">正在查询代币余额...</p>
          </CardBody>
        </Card>
      )}

      {/* 错误状态 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody>
            <p className="text-red-600">查询失败: {error.message}</p>
            <Button size="sm" color="danger" variant="flat" onPress={() => refetch()}>
              重试
            </Button>
          </CardBody>
        </Card>
      )}

      {/* 有余额的代币 */}
      {tokensWithBalance.length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-green-600">
              有余额的代币 ({tokensWithBalance.length})
            </h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-3">
              {tokensWithBalance.map((tokenBalance, index) => (
                <div
                  key={`${tokenBalance.token.address}-${index}`}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    {tokenBalance.token.logouri && (
                      <img
                        src={tokenBalance.token.logouri}
                        alt={tokenBalance.symbol}
                        className="w-8 h-8 rounded-full"
                      />
                    )}
                    <div>
                      <p className="font-medium">{tokenBalance.symbol}</p>
                      <p className="text-sm text-gray-500">{tokenBalance.name}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">
                      {formatBalance(tokenBalance.formattedBalance)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {tokenBalance.balance.toString()} (raw)
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}

      {/* 查询失败的代币 */}
      {tokensWithError.length > 0 && (
        <Card className="border-red-200">
          <CardHeader>
            <h3 className="text-lg font-semibold text-red-600">
              查询失败的代币 ({tokensWithError.length})
            </h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-2">
              {tokensWithError.map((tokenBalance, index) => (
                <div
                  key={`${tokenBalance.token.address}-${index}`}
                  className="flex items-center justify-between p-2 bg-red-50 rounded"
                >
                  <span className="font-medium">{tokenBalance.symbol}</span>
                  <span className="text-sm text-red-600">{tokenBalance.error}</span>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}

      {/* 零余额的代币（可选显示） */}
      {tokenBalances.length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-600">
              所有代币余额 ({tokenBalances.length})
            </h3>
          </CardHeader>
          <CardBody>
            <div className="max-h-60 overflow-y-auto space-y-1">
              {tokenBalances.map((tokenBalance, index) => (
                <div
                  key={`${tokenBalance.token.address}-${index}`}
                  className={`flex items-center justify-between p-2 rounded text-sm ${
                    tokenBalance.balance > 0n
                      ? 'bg-green-50'
                      : tokenBalance.error
                      ? 'bg-red-50'
                      : 'bg-gray-50'
                  }`}
                >
                  <span>{tokenBalance.symbol}</span>
                  <span>
                    {tokenBalance.error
                      ? '错误'
                      : formatBalance(tokenBalance.formattedBalance)}
                  </span>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
});

/**
 * 单个代币余额查询示例
 */
const SingleTokenBalanceExample = observer(({ token }: { token: any }) => {
  const { address } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();

  const currentNetwork = networks.find((net) => net.chainId === chainId);
  const evmNetwork = currentNetwork instanceof EvmNetwork ? currentNetwork : undefined;

  const { tokenBalance, isLoading, error, refetch } = useTokenBalance(
    token,
    address as Address,
    evmNetwork,
    {
      enabled: !!address && !!evmNetwork,
      refetchInterval: 30000,
    }
  );

  if (isLoading) {
    return <Spinner size="sm" />;
  }

  if (error) {
    return (
      <div className="text-red-500 text-sm">
        错误: {error.message}
        <Button size="sm" variant="flat" onPress={() => refetch()}>
          重试
        </Button>
      </div>
    );
  }

  if (!tokenBalance) {
    return <div className="text-gray-500 text-sm">无数据</div>;
  }

  return (
    <div className="flex items-center justify-between">
      <span>{tokenBalance.symbol}</span>
      <span>{formatBalance(tokenBalance.formattedBalance)}</span>
    </div>
  );
});

export { TokenBalancesExample, SingleTokenBalanceExample };
