import { Network } from "@/types/network.ts";
import { isAddress, Address, formatUnits } from "viem";
import Token from "@/types/token.ts";

// ERC20 标准 ABI - 只包含我们需要的方法
const ERC20_ABI = [
  {
    inputs: [{ name: 'account', type: 'address' }],
    name: 'balanceOf',
    outputs: [{ name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'decimals',
    outputs: [{ name: '', type: 'uint8' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'symbol',
    outputs: [{ name: '', type: 'string' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'name',
    outputs: [{ name: '', type: 'string' }],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

// 代币余额信息接口
export interface TokenBalance {
  token: Token;
  balance: bigint;
  decimals: number;
  formattedBalance: string;
  symbol: string;
  name: string;
  error?: string;
}

// MultiCall 合约配置
export interface MultiCallContract {
  address: Address;
  abi: any;
  functionName: string;
  args?: any[];
}

export class EvmNetwork extends Network {
  constructor(config: Partial<Network>) {
    super(config);
  }

  isAddressValid(address: string): boolean {
    return isAddress(address);
  }

  /**
   * 创建批量读取代币余额的合约调用配置
   * @param tokens 代币列表
   * @param userAddress 用户地址
   * @returns 合约调用配置数组
   */
  createTokenBalanceContracts(
    tokens: Token[],
    userAddress: Address
  ): MultiCallContract[] {
    const contracts: MultiCallContract[] = [];

    tokens.forEach((token) => {
      if (!token.address || !isAddress(token.address)) {
        return;
      }

      const tokenAddress = token.address as Address;

      // 添加余额查询
      contracts.push({
        address: tokenAddress,
        abi: ERC20_ABI,
        functionName: 'balanceOf',
        args: [userAddress],
      });

      // 添加精度查询（如果代币没有精度信息）
      if (!token.decimals) {
        contracts.push({
          address: tokenAddress,
          abi: ERC20_ABI,
          functionName: 'decimals',
        });
      }

      // 添加符号查询（如果代币没有符号信息）
      if (!token.symbol) {
        contracts.push({
          address: tokenAddress,
          abi: ERC20_ABI,
          functionName: 'symbol',
        });
      }

      // 添加名称查询（如果代币没有名称信息）
      if (!token.name) {
        contracts.push({
          address: tokenAddress,
          abi: ERC20_ABI,
          functionName: 'name',
        });
      }
    });

    return contracts;
  }

  /**
   * 处理 multiCall 结果并格式化为代币余额信息
   * @param tokens 原始代币列表
   * @param results multiCall 结果
   * @returns 格式化的代币余额信息数组
   */
  processTokenBalanceResults(
    tokens: Token[],
    results: any[]
  ): TokenBalance[] {
    const tokenBalances: TokenBalance[] = [];
    let resultIndex = 0;

    tokens.forEach((token) => {
      if (!token.address || !isAddress(token.address)) {
        // 跳过无效地址的代币
        tokenBalances.push({
          token,
          balance: 0n,
          decimals: token.decimals || 18,
          formattedBalance: '0',
          symbol: token.symbol || 'Unknown',
          name: token.name || 'Unknown Token',
          error: 'Invalid token address',
        });
        return;
      }

      try {
        // 获取余额结果
        const balanceResult = results[resultIndex++];
        const balance = balanceResult?.result || 0n;

        // 获取精度
        let decimals = token.decimals;
        if (!decimals) {
          const decimalsResult = results[resultIndex++];
          decimals = decimalsResult?.result || 18;
        }

        // 获取符号
        let symbol = token.symbol;
        if (!symbol) {
          const symbolResult = results[resultIndex++];
          symbol = symbolResult?.result || 'Unknown';
        }

        // 获取名称
        let name = token.name;
        if (!name) {
          const nameResult = results[resultIndex++];
          name = nameResult?.result || 'Unknown Token';
        }

        // 格式化余额
        const formattedBalance = formatUnits(balance as bigint, decimals);

        tokenBalances.push({
          token,
          balance: balance as bigint,
          decimals,
          formattedBalance,
          symbol,
          name,
        });
      } catch (error) {
        console.error(`Error processing token ${token.symbol}:`, error);
        tokenBalances.push({
          token,
          balance: 0n,
          decimals: token.decimals || 18,
          formattedBalance: '0',
          symbol: token.symbol || 'Unknown',
          name: token.name || 'Unknown Token',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    });

    return tokenBalances;
  }
}
