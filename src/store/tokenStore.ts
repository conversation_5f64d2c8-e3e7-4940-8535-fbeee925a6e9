import { makeAutoObservable } from "mobx";
import axios from "axios";
import Token from "@/types/token";

export class TokenStore {
  private readonly CACHE_DURATION = 3600000;
  private readonly CACHE_KEY = "token_list_cache";
  private readonly CACHE_TIMESTAMP_KEY = "token_list_cache_timestamp";

  tokenList: Token[] = [];

  constructor() {
    makeAutoObservable(this);
  }

  async getTokenList(chainId: number, destChainId: number) {
    const now = Date.now();
    const cacheKey = `${this.CACHE_KEY}_${chainId}_${destChainId}`;
    const timestampKey = `${this.CACHE_TIMESTAMP_KEY}_${chainId}_${destChainId}`;

    const cachedData = localStorage.getItem(cacheKey);
    const cachedTimestamp = localStorage.getItem(timestampKey);

    if (cachedData && cachedTimestamp) {
      const timestamp = parseInt(cachedTimestamp, 10);
      if (now - timestamp < this.CACHE_DURATION) {
        try {
          this.tokenList = JSON.parse(cachedData);
        } catch (error) {
          localStorage.removeItem(cacheKey);
          localStorage.removeItem(timestampKey);
        }
      }
    }

    try {
      const response = await axios.get(
        `http://localhost:9527/tube/getTokenList`,
        {
          params: {
            chainId,
            destChainId,
          },
        },
      );

      const tokens = response.data || [];

      if (tokens.length > 0) {
        localStorage.setItem(cacheKey, JSON.stringify(tokens));
        localStorage.setItem(timestampKey, now.toString());
      }

      this.tokenList = tokens;
    } catch (error) {
      console.error("Failed to fetch token list:", error);
      if (cachedData) {
        try {
          this.tokenList = JSON.parse(cachedData);
        } catch (parseError) {
          console.error(
            "Failed to parse cached token list as fallback:",
            parseError,
          );
        }
      }
      this.tokenList = [];
    }
  }

  /**
   * 清除缓存
   * @param chainId 可选，指定清除特定链的缓存
   * @param destChainId 可选，指定清除特定目标链的缓存
   */
  clearCache(chainId?: number, destChainId?: number): void {
    if (chainId !== undefined && destChainId !== undefined) {
      // 清除特定链的缓存
      const cacheKey = `${this.CACHE_KEY}_${chainId}_${destChainId}`;
      const timestampKey = `${this.CACHE_TIMESTAMP_KEY}_${chainId}_${destChainId}`;
      localStorage.removeItem(cacheKey);
      localStorage.removeItem(timestampKey);
    } else {
      // 清除所有相关缓存
      const keys = Object.keys(localStorage);
      keys.forEach((key) => {
        if (
          key.startsWith(this.CACHE_KEY) ||
          key.startsWith(this.CACHE_TIMESTAMP_KEY)
        ) {
          localStorage.removeItem(key);
        }
      });
    }
  }

  /**
   * 检查缓存是否有效
   * @param chainId 源链ID
   * @param destChainId 目标链ID
   */
  isCacheValid(chainId: number = 4689, destChainId: number = 1): boolean {
    const now = Date.now();
    const timestampKey = `${this.CACHE_TIMESTAMP_KEY}_${chainId}_${destChainId}`;
    const cachedTimestamp = localStorage.getItem(timestampKey);

    if (!cachedTimestamp) {
      return false;
    }

    const timestamp = parseInt(cachedTimestamp, 10);
    return now - timestamp < this.CACHE_DURATION;
  }
}
