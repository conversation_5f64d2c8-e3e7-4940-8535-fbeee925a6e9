import { 
  isValidEvmAddress, 
  validateAndFormatEvmAddress, 
  isEvmAddressEqual, 
  getShortEvmAddress 
} from "@/lib/utils/util";

/**
 * 简单的测试函数，用于验证 EVM 地址工具方法
 */
export function testEvmAddressUtils() {
  console.log("🧪 开始测试 EVM 地址工具方法...\n");

  // 测试数据
  const validAddress = "0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a";
  const validAddressLowercase = "0x742d35cc6634c0532925a3b8d4c9db96c4b4df8a";
  const invalidAddressShort = "0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df";
  const invalidAddressNoPrefix = "742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a";
  const invalidAddressHex = "0xGGGd35Cc6634C0532925a3b8D4C9db96C4b4Df8a";
  const zeroAddress = "0x0000000000000000000000000000000000000000";
  const emptyString = "";
  const nullValue = null;

  // 测试 isValidEvmAddress
  console.log("📋 测试 isValidEvmAddress:");
  console.log(`✅ 有效地址: ${isValidEvmAddress(validAddress)}`); // 应该是 true
  console.log(`✅ 有效地址(小写): ${isValidEvmAddress(validAddressLowercase)}`); // 应该是 true
  console.log(`❌ 无效地址(太短): ${isValidEvmAddress(invalidAddressShort)}`); // 应该是 false
  console.log(`❌ 无效地址(无前缀): ${isValidEvmAddress(invalidAddressNoPrefix)}`); // 应该是 false
  console.log(`❌ 无效地址(非十六进制): ${isValidEvmAddress(invalidAddressHex)}`); // 应该是 false
  console.log(`✅ 零地址: ${isValidEvmAddress(zeroAddress)}`); // 应该是 true
  console.log(`❌ 空字符串: ${isValidEvmAddress(emptyString)}`); // 应该是 false
  console.log(`❌ null值: ${isValidEvmAddress(nullValue)}`); // 应该是 false
  console.log("");

  // 测试 validateAndFormatEvmAddress
  console.log("📋 测试 validateAndFormatEvmAddress:");
  console.log(`有效地址格式化: ${validateAndFormatEvmAddress(validAddress)}`);
  console.log(`小写地址格式化: ${validateAndFormatEvmAddress(validAddressLowercase)}`);
  console.log(`无效地址格式化: ${validateAndFormatEvmAddress(invalidAddressShort)}`); // 应该是 null
  console.log(`零地址格式化: ${validateAndFormatEvmAddress(zeroAddress)}`);
  console.log("");

  // 测试 isEvmAddressEqual
  console.log("📋 测试 isEvmAddressEqual:");
  console.log(`相同地址(不同大小写): ${isEvmAddressEqual(validAddress, validAddressLowercase)}`); // 应该是 true
  console.log(`不同地址: ${isEvmAddressEqual(validAddress, zeroAddress)}`); // 应该是 false
  console.log(`有效地址 vs 无效地址: ${isEvmAddressEqual(validAddress, invalidAddressShort)}`); // 应该是 false
  console.log(`两个无效地址: ${isEvmAddressEqual(invalidAddressShort, invalidAddressNoPrefix)}`); // 应该是 false
  console.log("");

  // 测试 getShortEvmAddress
  console.log("📋 测试 getShortEvmAddress:");
  console.log(`默认简短格式: ${getShortEvmAddress(validAddress)}`);
  console.log(`自定义简短格式(8,6): ${getShortEvmAddress(validAddress, 8, 6)}`);
  console.log(`自定义简短格式(10,8): ${getShortEvmAddress(validAddress, 10, 8)}`);
  console.log(`无效地址简短格式: ${getShortEvmAddress(invalidAddressShort)}`);
  console.log(`空字符串简短格式: "${getShortEvmAddress(emptyString)}"`);
  console.log(`null值简短格式: "${getShortEvmAddress(nullValue)}"`);
  console.log("");

  console.log("✅ 测试完成！");
}

/**
 * 在浏览器控制台中运行测试
 * 使用方法：在浏览器控制台中输入 window.testEvmAddressUtils()
 */
if (typeof window !== "undefined") {
  (window as any).testEvmAddressUtils = testEvmAddressUtils;
}
