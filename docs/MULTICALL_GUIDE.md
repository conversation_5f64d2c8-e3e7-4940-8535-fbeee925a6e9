# MultiCall 批量读取代币余额指南

本指南介绍如何在 EvmNetwork 中使用 multiCall 功能来批量读取代币余额。

## 概述

MultiCall 功能允许你在一次区块链调用中批量查询多个代币的余额，大大提高了查询效率并减少了 RPC 调用次数。

## 核心组件

### 1. EvmNetwork 类

`src/lib/EvmNetwork.ts` 中的 `EvmNetwork` 类提供了以下核心方法：

- `createTokenBalanceContracts()` - 创建批量合约调用配置
- `processTokenBalanceResults()` - 处理 multiCall 结果

### 2. useTokenBalances Hook

`src/hooks/useTokenBalances.ts` 提供了以下 Hooks：

- `useTokenBalances()` - 批量查询多个代币余额
- `useTokenBalance()` - 查询单个代币余额
- `useUserTokenBalances()` - 查询用户所有代币余额

## 基本使用

### 1. 批量查询代币余额

```tsx
import { useTokenBalances } from '@/hooks/useTokenBalances';
import { EvmNetwork } from '@/lib/EvmNetwork';

function TokenBalancesList() {
  const { address } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const { tokenStore } = useStore();

  // 获取当前网络
  const currentNetwork = networks.find((net) => net.chainId === chainId);
  const evmNetwork = currentNetwork instanceof EvmNetwork ? currentNetwork : undefined;

  // 批量查询代币余额
  const {
    tokenBalances,
    tokensWithBalance,
    isLoading,
    error,
    refetch,
  } = useTokenBalances(
    tokenStore.tokenList, // 代币列表
    address as Address,   // 用户地址
    evmNetwork,          // 网络实例
    {
      enabled: !!address && !!evmNetwork,
      refetchInterval: 30000, // 30秒自动刷新
    }
  );

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  return (
    <div>
      <h3>有余额的代币 ({tokensWithBalance.length})</h3>
      {tokensWithBalance.map((tokenBalance) => (
        <div key={tokenBalance.token.address}>
          <span>{tokenBalance.symbol}: </span>
          <span>{tokenBalance.formattedBalance}</span>
        </div>
      ))}
    </div>
  );
}
```

### 2. 查询单个代币余额

```tsx
import { useTokenBalance } from '@/hooks/useTokenBalances';

function SingleTokenBalance({ token }) {
  const { address } = useAppKitAccount();
  const evmNetwork = getCurrentNetwork(); // 获取当前网络

  const { tokenBalance, isLoading, error } = useTokenBalance(
    token,
    address as Address,
    evmNetwork
  );

  if (isLoading) return <span>加载中...</span>;
  if (error) return <span>错误</span>;

  return (
    <span>
      {tokenBalance?.formattedBalance || '0'} {tokenBalance?.symbol}
    </span>
  );
}
```

## 高级功能

### 1. 自定义查询选项

```tsx
const { tokenBalances } = useTokenBalances(
  tokens,
  userAddress,
  network,
  {
    enabled: !!userAddress,           // 控制是否启用查询
    refetchInterval: 30000,           // 自动刷新间隔（毫秒）
    staleTime: 10000,                // 数据新鲜度时间
  }
);
```

### 2. 过滤和统计

```tsx
const {
  tokenBalances,        // 所有代币余额
  tokensWithBalance,    // 有余额的代币
  tokensWithError,      // 查询失败的代币
  totalTokens,          // 总代币数
  tokensWithBalanceCount, // 有余额的代币数量
  tokensWithErrorCount,   // 查询失败的代币数量
} = useTokenBalances(tokens, userAddress, network);
```

### 3. 手动刷新

```tsx
const { refetch, isRefetching } = useTokenBalances(tokens, userAddress, network);

// 手动刷新
const handleRefresh = () => {
  refetch();
};
```

## 数据结构

### TokenBalance 接口

```typescript
interface TokenBalance {
  token: Token;              // 原始代币信息
  balance: bigint;           // 原始余额（wei）
  decimals: number;          // 代币精度
  formattedBalance: string;  // 格式化后的余额
  symbol: string;            // 代币符号
  name: string;              // 代币名称
  error?: string;            // 错误信息（如果有）
}
```

## 性能优化

### 1. 启用条件控制

```tsx
const { tokenBalances } = useTokenBalances(
  tokens,
  userAddress,
  network,
  {
    enabled: !!userAddress && !!network && tokens.length > 0,
  }
);
```

### 2. 数据缓存

Hook 内部使用了 React Query 的缓存机制：

- `staleTime`: 数据新鲜度时间，在此时间内不会重新请求
- `refetchInterval`: 自动刷新间隔
- `retry`: 失败重试次数和策略

### 3. 批量大小控制

对于大量代币，可以考虑分批查询：

```tsx
// 将代币列表分批处理
const batchSize = 50;
const tokenBatches = [];
for (let i = 0; i < tokens.length; i += batchSize) {
  tokenBatches.push(tokens.slice(i, i + batchSize));
}

// 分别查询每批代币
const batchResults = tokenBatches.map(batch => 
  useTokenBalances(batch, userAddress, network)
);
```

## 错误处理

### 1. 网络错误

```tsx
const { error, refetch } = useTokenBalances(tokens, userAddress, network);

if (error) {
  return (
    <div>
      <p>查询失败: {error.message}</p>
      <button onClick={() => refetch()}>重试</button>
    </div>
  );
}
```

### 2. 单个代币错误

```tsx
const { tokensWithError } = useTokenBalances(tokens, userAddress, network);

if (tokensWithError.length > 0) {
  console.log('查询失败的代币:', tokensWithError);
}
```

## 最佳实践

1. **合理设置刷新间隔**: 根据应用需求设置合适的 `refetchInterval`
2. **使用启用条件**: 通过 `enabled` 选项控制查询时机
3. **处理加载状态**: 为用户提供清晰的加载反馈
4. **错误处理**: 妥善处理网络错误和单个代币查询失败
5. **性能监控**: 监控查询性能，必要时进行分批处理

## 示例组件

完整的示例组件可以在 `src/examples/TokenBalancesExample.tsx` 中找到，包括：

- 批量余额查询
- 统计信息显示
- 错误处理
- 加载状态
- 手动刷新功能
